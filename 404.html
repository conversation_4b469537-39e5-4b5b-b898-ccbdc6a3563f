<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow">
    <title>Page Not Found - Stress Free Events & Weddings</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .error-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--white);
        }
        .error-content {
            max-width: 600px;
        }
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            margin-bottom: 20px;
            opacity: 0.8;
        }
        .error-message {
            font-size: 1.5rem;
            margin-bottom: 30px;
        }
        .error-description {
            font-size: 1.1rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        .btn-home {
            display: inline-block;
            padding: 15px 30px;
            background-color: var(--white);
            color: var(--primary-color);
            text-decoration: none;
            border-radius: 5px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="error-page">
        <div class="error-content">
            <div class="error-code">404</div>
            <h1 class="error-message">Oops! Page Not Found</h1>
            <p class="error-description">
                The page you're looking for seems to have wandered off. 
                Don't worry, we'll help you find your way back to planning your perfect wedding!
            </p>
            <a href="home.html" class="btn-home">Return to Home</a>
        </div>
    </div>
</body>
</html>
