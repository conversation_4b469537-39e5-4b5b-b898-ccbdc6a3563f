# Nginx configuration for Stress Free Events & Weddings
# Place this in your nginx server block or use as reference

server {
    listen 80;
    server_name www.stressfree-events.com stressfree-events.com;
    
    # Document root
    root /usr/share/nginx/html;
    
    # Set index.html as default, fallback to home.html
    index index.html home.html;

    # Main location block
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Handle specific route for home.html (keep it accessible)
    location = /home.html {
        try_files $uri =404;
    }
    
    # Handle sitemap.xml
    location = /sitemap.xml {
        add_header Content-Type application/xml;
        try_files $uri =404;
    }
    
    # Handle robots.txt
    location = /robots.txt {
        add_header Content-Type text/plain;
        try_files $uri =404;
    }
    
    # Optimize static files
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # Custom error pages
    error_page 404 /404.html;
    
    # Prevent access to hidden files
    location ~ /\. {
        deny all;
    }
}
