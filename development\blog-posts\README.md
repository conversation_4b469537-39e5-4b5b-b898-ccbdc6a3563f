# Blog Post Templates

This directory contains templates and examples for blog posts on the Stress Free Events and Weddings website.

## How to Use These Templates

1. Choose a template that matches the type of blog post you want to create
2. Copy the template file and rename it with your blog post title (use hyphens instead of spaces)
3. Replace the placeholder content with your actual content
4. Update the metadata (title, description, keywords, etc.)
5. Add your images to the appropriate directory
6. Link to the new blog post from the main blog page

## Available Templates

- `wedding-tips-template.html` - Template for wedding planning tips and advice
- `vendor-spotlight-template.html` - Template for featuring wedding vendors
- `real-wedding-template.html` - Template for showcasing real weddings
- `wedding-trends-template.html` - Template for discussing wedding trends

## Blog Post Structure

Each blog post should include:

1. **Hero Section** - Large image with title and metadata
2. **Introduction** - Brief overview of the topic
3. **Main Content** - Detailed information, broken into sections with headings
4. **Images** - High-quality, relevant images
5. **Conclusion** - Summary or call-to-action
6. **Tags** - Relevant categories and keywords
7. **Share Buttons** - Social media sharing options
8. **Related Posts** - Links to similar content

## Image Guidelines

- Hero images should be 1920px × 1080px
- In-content images should be 800px-1200px wide
- All images should be optimized for web (compressed)
- Use descriptive alt text for accessibility
- Store images in the `images/blog/` directory

## SEO Best Practices

- Use descriptive, keyword-rich titles
- Write meta descriptions that are 150-160 characters
- Include relevant keywords naturally throughout the content
- Use proper heading hierarchy (H1, H2, H3)
- Add alt text to all images
- Link to other relevant content on your site

## Publishing Workflow

1. Create the blog post using a template
2. Review and edit the content
3. Optimize images and add them to the appropriate directory
4. Test the blog post to ensure all links and images work
5. Add the blog post to the main blog page
6. Share on social media

For more detailed instructions, refer to the [Blog Documentation](../documentation/page-specific/blog-page.md).
