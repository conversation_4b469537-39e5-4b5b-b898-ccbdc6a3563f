<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow">
    <title>URL Removal Guide - Internal Use Only</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .url-list { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007cba; background: #f9f9f9; }
        code { background: #e8e8e8; padding: 2px 5px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>Google Search Console - URL Removal Guide</h1>
    
    <div class="step">
        <h2>Step 1: Remove These Problematic URLs</h2>
        <p>Go to Google Search Console → Removals → New Request → Temporarily remove URL</p>
        
        <div class="url-list">
            <h3>URLs to Remove:</h3>
            <ul>
                <li><code>https://www.stressfree-events.com/?fbclid=IwAR2nKQqddkManaGnx1M4CUjNnC1KFO9LJ4CZkbmhczFubsr4Jh-R52PapPY</code></li>
                <li><code>https://www.stressfree-events.com/?utm_source=weddingwire.com&utm_medium=referral&utm_campaign=WeddingWire</code></li>
            </ul>
        </div>
    </div>

    <div class="step">
        <h2>Step 2: Request Re-indexing of Clean URLs</h2>
        <p>Use URL Inspection tool to request indexing of:</p>
        <ul>
            <li><code>https://www.stressfree-events.com/</code></li>
            <li><code>https://www.stressfree-events.com/home.html</code></li>
        </ul>
    </div>

    <div class="step">
        <h2>Step 3: Monitor Progress</h2>
        <p>Check the "Page indexing" report in 24-48 hours to see if issues are resolved.</p>
    </div>

    <div class="step">
        <h2>Changes Made to Fix Issues:</h2>
        <ul>
            <li>✅ Updated nginx.conf to redirect URLs with query parameters</li>
            <li>✅ Modified index.html to handle tracking parameters properly</li>
            <li>✅ Added robots.txt rules to prevent indexing of tracking URLs</li>
            <li>✅ Updated sitemap.xml with proper URL structure</li>
            <li>✅ Fixed canonical tags to point to clean URLs</li>
        </ul>
    </div>
</body>
</html>
